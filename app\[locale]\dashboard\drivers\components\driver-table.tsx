"use client"

import * as React from "react"
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  Eye,
  ShieldCheck,
  ShieldAlert,
  FileText,
  TrendingUp,
  Search,
  Star,
} from "lucide-react"
import type { Driver, UserRole } from "@/types" // Import UserRole
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import Link from "next/link"

const getDriverStatusBadgeClass = (status: Driver["status"]) => {
  switch (status) {
    case "approved":
    case "active_online":
    case "active_offline":
      return "bg-status-green text-status-green-foreground hover:bg-status-green/90"
    case "pending":
      return "bg-status-yellow text-status-yellow-foreground hover:bg-status-yellow/90"
    case "rejected":
      return "bg-status-red text-status-red-foreground hover:bg-status-red/90"
    default:
      return "bg-status-gray text-status-gray-foreground hover:bg-status-gray/90"
  }
}

export function DriverTable({ data, currentUserRole }: { data: Driver[]; currentUserRole: UserRole }) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const { toast } = useToast()
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = React.useState(false)
  const [actionToConfirm, setActionToConfirm] = React.useState<{
    action: string
    driverId: string
    driverName: string
  } | null>(null)

  const confirmAction = () => {
    if (actionToConfirm) {
      toast({
        title: `Driver Action: ${actionToConfirm.action}`,
        description: `Action '${actionToConfirm.action}' on driver '${actionToConfirm.driverName}' (ID: ${actionToConfirm.driverId}) confirmed. (Mocked)`,
      })
      setIsConfirmDeleteDialogOpen(false)
      setActionToConfirm(null)
    }
  }

  const openConfirmationDialog = (action: string, driver: Driver) => {
    setActionToConfirm({ action, driverId: driver.id, driverName: driver.name })
    setIsConfirmDeleteDialogOpen(true)
  }

  // Define permissions based on role
  const canEditDriver = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canManageStatus = currentUserRole === "admin" || currentUserRole === "regional_head" // Approve/Reject
  const canDeleteDriver = currentUserRole === "admin"
  const canViewFullDetails =
    currentUserRole === "admin" || currentUserRole === "regional_head" || currentUserRole === "regional_operator" // Operator can view for support
  const canViewDocuments = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canViewPerformance = currentUserRole === "admin" || currentUserRole === "regional_head"

  const columns: ColumnDef<Driver>[] = [
    {
      accessorKey: "profilePictureUrl",
      header: "",
      cell: ({ row }) => (
        <Image
          src={row.original.profilePictureUrl || "/placeholder.svg?width=40&height=40&query=Driver+Avatar"}
          alt={row.original.name}
          width={36}
          height={36}
          className="rounded-full object-cover"
        />
      ),
      size: 50,
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <div className="font-medium text-foreground">{row.original.name}</div>,
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: ({ row }) => <div className="text-muted-foreground">{row.original.phone}</div>,
    },
    {
      accessorKey: "vehicleInfo",
      header: "Vehicle",
      cell: ({ row }) => <div className="text-muted-foreground truncate max-w-[150px]">{row.original.vehicleInfo}</div>,
    },
    {
      accessorKey: "rating",
      header: "Rating",
      cell: ({ row }) => (
        <div className="flex items-center text-muted-foreground">
          {row.original.rating > 0 ? (
            <>
              <Star className="h-4 w-4 text-yellow-400 mr-1 fill-yellow-400" />
              {row.original.rating.toFixed(1)}
            </>
          ) : (
            "N/A"
          )}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status
        return (
          <Badge variant="outline" className={cn("capitalize border", getDriverStatusBadgeClass(status))}>
            {status.replace(/_/g, " ")}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const driver = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground hover:bg-secondary"
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-card border-border shadow-lg">
              <DropdownMenuLabel className="text-foreground">Actions</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-border" />
              {canViewFullDetails && (
                <DropdownMenuItem asChild className="hover:!bg-secondary focus:!bg-secondary cursor-pointer">
                  <Link href={`/dashboard/drivers/${driver.id}`} className="flex items-center w-full">
                    <Eye className="mr-2 h-4 w-4 text-blue-500" /> View Details
                  </Link>
                </DropdownMenuItem>
              )}
              {canEditDriver && (
                <DropdownMenuItem
                  onClick={() => openConfirmationDialog("Edit Driver", driver)}
                  className="hover:!bg-secondary focus:!bg-secondary"
                >
                  <Edit className="mr-2 h-4 w-4 text-yellow-500" /> Edit Driver
                </DropdownMenuItem>
              )}
              {canViewDocuments && (
                <DropdownMenuItem
                  onClick={() => openConfirmationDialog("View Documents", driver)}
                  className="hover:!bg-secondary focus:!bg-secondary"
                >
                  <FileText className="mr-2 h-4 w-4 text-purple-500" /> View Documents
                </DropdownMenuItem>
              )}
              {canViewPerformance && (
                <DropdownMenuItem
                  onClick={() => openConfirmationDialog("Performance", driver)}
                  className="hover:!bg-secondary focus:!bg-secondary"
                >
                  <TrendingUp className="mr-2 h-4 w-4 text-indigo-500" /> Performance
                </DropdownMenuItem>
              )}
              {(canManageStatus || canDeleteDriver) && <DropdownMenuSeparator className="bg-border" />}

              {canManageStatus && driver.status === "pending" && (
                <>
                  <DropdownMenuItem
                    onClick={() => openConfirmationDialog("Approve Driver", driver)}
                    className="text-status-green hover:!bg-status-green/10 focus:!bg-status-green/10"
                  >
                    <ShieldCheck className="mr-2 h-4 w-4" /> Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => openConfirmationDialog("Reject Driver", driver)}
                    className="text-status-red hover:!bg-status-red/10 focus:!bg-status-red/10"
                  >
                    <ShieldAlert className="mr-2 h-4 w-4" /> Reject
                  </DropdownMenuItem>
                </>
              )}
              {canDeleteDriver && (
                <DropdownMenuItem
                  onClick={() => openConfirmationDialog("Delete Driver", driver)}
                  className="text-status-red hover:!bg-status-red/10 focus:!bg-status-red/10"
                >
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Driver
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: { pageSize: 5 },
    },
  })

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between gap-2">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Filter by name or vehicle..."
            value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
            onChange={(event) => {
              table.getColumn("name")?.setFilterValue(event.target.value)
            }}
            className="pl-10 bg-background border-border focus:ring-ring"
          />
        </div>
      </div>
      <Card className="border-border shadow-sm">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-border">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-muted-foreground">
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="border-border hover:bg-secondary/50 transition-colors"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-3">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-muted-foreground">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className="bg-background text-foreground hover:bg-secondary"
        >
          Previous
        </Button>
        <span className="text-sm text-muted-foreground">
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          className="bg-background text-foreground hover:bg-secondary"
        >
          Next
        </Button>
      </div>
      <AlertDialog open={isConfirmDeleteDialogOpen} onOpenChange={setIsConfirmDeleteDialogOpen}>
        <AlertDialogContent className="bg-card border-border">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-foreground">Are you sure?</AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              You are about to {actionToConfirm?.action.toLowerCase()} driver '{actionToConfirm?.driverName}'. This
              action might be irreversible.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-background text-foreground hover:bg-secondary">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmAction}
              className={cn(
                actionToConfirm?.action.toLowerCase().includes("delete") ||
                  actionToConfirm?.action.toLowerCase().includes("reject")
                  ? "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  : "bg-primary text-primary-foreground hover:bg-primary/90",
              )}
            >
              Confirm {actionToConfirm?.action}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
