"use client"

import type React from "react"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/context/auth-context"
import { ArrowRight, Shield, Users, MapPin, Clock } from "lucide-react"

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push("/dashboard")
    }
  }, [isAuthenticated, isLoading, router])

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render homepage if user is authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image 
              src="/subhyatra-logo.svg" 
              alt="SubhYatra Logo" 
              width={40} 
              height={40} 
              className="h-10 w-10"
            />
            <h1 className="text-2xl font-bold text-primary">SubhYatra</h1>
          </div>
          <Link href="/login">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              Login
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <div className="mb-8">
            <Image 
              src="/subhyatra-logo.svg" 
              alt="SubhYatra Logo" 
              width={120} 
              height={120} 
              className="mx-auto mb-6"
            />
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Welcome to{" "}
            <span className="text-primary">SubhYatra</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
            Your trusted ride-hailing platform connecting passengers with reliable drivers across Nepal
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/login">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg">
                Access Management Center
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">Safe & Secure</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Verified drivers and secure payment systems ensure your safety and peace of mind
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Users className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">Trusted Community</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Join thousands of satisfied passengers and professional drivers across Nepal
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <MapPin className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">Wide Coverage</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Available in major cities including Kathmandu, Pokhara, and expanding nationwide
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Clock className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">24/7 Service</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Round-the-clock availability with quick response times for all your travel needs
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Management Center Access */}
        <div className="max-w-2xl mx-auto text-center">
          <Card className="bg-muted/50 border-primary/20">
            <CardHeader>
              <CardTitle className="text-2xl text-primary">Management Center</CardTitle>
              <CardDescription className="text-lg">
                Access the administrative dashboard to manage operations, monitor performance, and oversee the SubhYatra platform
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Link href="/login">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3">
                  Login to Dashboard
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/30 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <Image 
                src="/subhyatra-logo.svg" 
                alt="SubhYatra Logo" 
                width={32} 
                height={32} 
                className="h-8 w-8"
              />
              <span className="text-lg font-semibold text-primary">SubhYatra</span>
            </div>
            <p className="text-muted-foreground text-center md:text-right">
              © 2024 SubhYatra. Connecting Nepal, one ride at a time.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
