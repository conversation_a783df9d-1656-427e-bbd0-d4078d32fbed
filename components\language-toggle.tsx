"use client"

import * as React from "react"
import { useRouter, usePathname } from "next/navigation"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Globe } from "lucide-react"
import { locales } from "@/i18n/request"

export function LanguageToggle() {
  const t = useTranslations('navigation')
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const switchLanguage = (newLocale: string) => {
    // Remove the current locale from the pathname
    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/'
    
    // Navigate to the new locale
    router.push(`/${newLocale}${pathWithoutLocale}`)
  }

  const getLanguageLabel = (localeCode: string) => {
    switch (localeCode) {
      case 'en':
        return 'EN'
      case 'ne':
        return 'नेपाली'
      default:
        return localeCode.toUpperCase()
    }
  }

  const getLanguageName = (localeCode: string) => {
    switch (localeCode) {
      case 'en':
        return t('english')
      case 'ne':
        return t('nepali')
      default:
        return localeCode
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">{getLanguageLabel(locale)}</span>
          <span className="sr-only">{t('language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {locales.map((localeOption) => (
          <DropdownMenuItem
            key={localeOption}
            onClick={() => switchLanguage(localeOption)}
            className={locale === localeOption ? "bg-accent" : ""}
          >
            <span className="flex items-center gap-2">
              {getLanguageLabel(localeOption)}
              <span className="text-muted-foreground">
                {getLanguageName(localeOption)}
              </span>
            </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
