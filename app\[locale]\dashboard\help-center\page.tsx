"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useToast } from "@/components/ui/use-toast"

const faqs = [
  {
    question: "How do I reset my password?",
    answer:
      "You can reset your password from the Account Settings page if you remember your current password. If you've forgotten it, please contact an administrator.",
  },
  {
    question: "How is my data filtered by region?",
    answer:
      "If your role is Regional Head or Regional Operator, most data tables (like Users, Drivers, Rides) will automatically be filtered to show only entries relevant to your assigned region.",
  },
  {
    question: "What are the monetary limits for Regional Heads?",
    answer:
      "Regional Heads have certain monetary limits for actions like resolving ride disputes or adjusting fares (e.g., up to $100). Promotions created by Regional Heads also have limits (e.g., up to $200 per campaign). Actions exceeding these limits require Admin approval.",
  },
  {
    question: "Can Regional Operators issue refunds?",
    answer:
      "Regional Operators can issue basic refunds up to a certain limit (e.g., $25). Larger refunds need to be escalated.",
  },
]

export default function HelpCenterPage() {
  const { toast } = useToast()

  const handleSubmitSupportRequest = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    toast({
      title: "Support Request Submitted (Mock)",
      description: "Your support request has been received. We will get back to you shortly. (This is a mock action)",
    })
    // Reset form logic here
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Help Center</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-semibold">Help Center</h1>

      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions (FAQs)</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem value={`item-${index}`} key={index}>
                <AccordionTrigger>{faq.question}</AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Contact Support</CardTitle>
          <CardDescription>If you can't find an answer in the FAQs, please submit a support request.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmitSupportRequest} className="space-y-4">
            <div className="space-y-1">
              <Label htmlFor="supportSubject">Subject</Label>
              <Input id="supportSubject" placeholder="e.g., Issue with payment report" required />
            </div>
            <div className="space-y-1">
              <Label htmlFor="supportDescription">Description</Label>
              <Textarea
                id="supportDescription"
                placeholder="Please describe your issue in detail..."
                rows={5}
                required
              />
            </div>
            <Button type="submit">Submit Request</Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p>
            <strong>Support Email:</strong> <EMAIL> (Placeholder)
          </p>
          <p>
            <strong>Support Phone:</strong> +977-1-XXXXXXX (Placeholder)
          </p>
          <p>
            <strong>Operating Hours:</strong> Sunday - Friday, 9 AM - 6 PM NPT
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
