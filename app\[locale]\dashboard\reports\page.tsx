"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context" // Added
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert" // Added
import { Terminal } from "lucide-react" // Added

const DatePickerWithRangePlaceholder = ({ className }: { className?: string }) => (
  <div className={`p-2 border rounded-md ${className}`}>Date Range Picker Placeholder</div>
)

export default function FinancialReportsPage() {
  const { toast } = useToast()
  const { user } = useAuth() // Added
  const role = user?.role || "regional_operator" // Added
  const region = user?.region // Added

  // Permissions
  const canAccessFinancialReports = role === "admin" || role === "regional_head"
  const canGenerateAllReportTypes = role === "admin"

  const handleGenerateReport = (reportType?: string) => {
    toast({
      title: "Generate Report",
      description: `Report generation for ${reportType || "selected type"} is a placeholder. (Role: ${role})`,
    })
  }

  const handleExportData = (format: string, dataType?: string) => {
    toast({
      title: `Export Data as ${format.toUpperCase()}`,
      description: `Data export for ${dataType || "selected type"} is a placeholder. (Role: ${role})`,
    })
  }

  if (!canAccessFinancialReports) {
    // Added check for operator
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-semibold">Financial Reports & Exports</h1>
        <Alert>
          <Terminal className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to access financial reports.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">
        Financial Reports & Exports {role === "regional_head" && region ? `(${region})` : ""}
      </h1>

      <Card>
        <CardHeader>
          <CardTitle>Generate Financial Report</CardTitle>
          <CardDescription>Select report type and date range.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="reportType" className="block text-sm font-medium text-muted-foreground mb-1">
                Report Type
              </label>
              <Select disabled={!canAccessFinancialReports}>
                <SelectTrigger id="reportType">
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  {canGenerateAllReportTypes && <SelectItem value="revenue">Revenue Summary (System-wide)</SelectItem>}
                  {role === "regional_head" && (
                    <SelectItem value="regional_revenue">Regional Revenue Summary</SelectItem>
                  )}
                  {canGenerateAllReportTypes && <SelectItem value="payouts">Driver Payouts (System-wide)</SelectItem>}
                  {role === "regional_head" && (
                    <SelectItem value="regional_payouts">Regional Driver Payouts</SelectItem>
                  )}
                  {canGenerateAllReportTypes && (
                    <SelectItem value="commissions">Commissions Earned (System-wide)</SelectItem>
                  )}
                  {canGenerateAllReportTypes && (
                    <SelectItem value="transactions_all">All Transactions (System-wide)</SelectItem>
                  )}
                  {role === "regional_head" && (
                    <SelectItem value="regional_transactions">Regional Transactions</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <label htmlFor="dateRange" className="block text-sm font-medium text-muted-foreground mb-1">
                Date Range
              </label>
              <DatePickerWithRangePlaceholder className="w-full" />
            </div>
          </div>
          <Button
            onClick={() => handleGenerateReport()}
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
            disabled={!canAccessFinancialReports}
          >
            <Download className="mr-2 h-4 w-4" /> Generate Report
          </Button>
        </CardContent>
      </Card>

      {canGenerateAllReportTypes && ( // Data export might be admin only or have regional variants
        <Card>
          <CardHeader>
            <CardTitle>Export Data (Admin)</CardTitle>
            <CardDescription>Export raw data for external analysis.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="exportDataType" className="block text-sm font-medium text-muted-foreground mb-1">
                  Data Type
                </label>
                <Select>
                  <SelectTrigger id="exportDataType">
                    <SelectValue placeholder="Select data to export" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="users">Users</SelectItem>
                    <SelectItem value="drivers">Drivers</SelectItem>
                    <SelectItem value="rides">Rides</SelectItem>
                    <SelectItem value="payments">Payments</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <label htmlFor="exportDateRange" className="block text-sm font-medium text-muted-foreground mb-1">
                  Date Range
                </label>
                <DatePickerWithRangePlaceholder className="w-full" />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => handleExportData("csv")}
                variant="outline"
                className="bg-background text-foreground"
              >
                <Download className="mr-2 h-4 w-4" /> Export as CSV
              </Button>
              <Button
                onClick={() => handleExportData("excel")}
                variant="outline"
                className="bg-background text-foreground"
              >
                <Download className="mr-2 h-4 w-4" /> Export as Excel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
