"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context" // Added
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert" // Added
import { Terminal } from "lucide-react" // Added

export default function SettingsPage() {
  const { toast } = useToast()
  const { user } = useAuth() // Added
  const role = user?.role || "regional_operator" // Added
  const region = user?.region // Added

  // Permissions based on role
  const canManageFareRules = role === "admin"
  const canManageGlobalGeoZones = role === "admin"
  const canManageRegionalGeoZones = role === "regional_head"
  const canManageGlobalPromotions = role === "admin"
  const canManageRegionalPromotions = role === "regional_head" // with $200 limit
  const canManageAppVersions = role === "admin"

  const handleSaveChanges = (section: string, details?: any) => {
    let message = `Saving for ${section}`
    if (details?.limitExceeded) {
      message = `Action for ${section} exceeds limit for ${role}. Requires Admin approval.`
      toast({ title: "Limit Exceeded", description: message, variant: "destructive" })
      return
    }
    message += ` is a placeholder. API call would be made here. (Role: ${role})`
    toast({
      title: `Save ${section} Settings`,
      description: message,
    })
  }

  if (role === "regional_operator") {
    // Added check for operator
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-semibold">System Settings</h1>
        <Alert>
          <Terminal className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>Regional Operators do not have access to System Settings.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">
        System Settings {role === "regional_head" && region ? `(${region} Region)` : ""}
      </h1>

      {canManageFareRules && (
        <Card>
          <CardHeader>
            <CardTitle>Fare Calculation Rules</CardTitle>
            <CardDescription>Manage how ride fares are calculated. (Admin Only)</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="baseFare">Base Fare (रू)</Label>
              <Input id="baseFare" type="number" defaultValue="50" disabled={!canManageFareRules} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="perKmRate">Rate per KM (रू)</Label>
              <Input id="perKmRate" type="number" defaultValue="15" disabled={!canManageFareRules} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="perMinuteRate">Rate per Minute (रू)</Label>
              <Input id="perMinuteRate" type="number" defaultValue="2" disabled={!canManageFareRules} />
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="surgePricing" disabled={!canManageFareRules} />
              <Label htmlFor="surgePricing">Enable Surge Pricing</Label>
            </div>
            <Button
              onClick={() => handleSaveChanges("Fare Rules")}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={!canManageFareRules}
            >
              Save Fare Rules
            </Button>
          </CardContent>
        </Card>
      )}

      {(canManageGlobalGeoZones || canManageRegionalGeoZones) && (
        <Card>
          <CardHeader>
            <CardTitle>Geographic Zone Management</CardTitle>
            <CardDescription>
              {canManageGlobalGeoZones
                ? "Define and modify service areas system-wide. (Admin Only)"
                : `Adjust operational zones within ${region || "your assigned"} region. (Regional Head)`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center bg-muted/50 rounded-md">
              <p className="text-muted-foreground">Map Placeholder for Zone Management</p>
            </div>
            <Button
              onClick={() => handleSaveChanges("Geo Zones")}
              className="mt-4 bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={!(canManageGlobalGeoZones || canManageRegionalGeoZones)}
            >
              Save Zones
            </Button>
          </CardContent>
        </Card>
      )}

      {(canManageGlobalPromotions || canManageRegionalPromotions) && (
        <Card>
          <CardHeader>
            <CardTitle>Promotional Campaigns & Coupons</CardTitle>
            <CardDescription>
              {canManageGlobalPromotions
                ? "Create and manage promotions system-wide. (Admin Only)"
                : `Create regional promotions (up to $200 limit per campaign) for ${region || "your assigned"} region. (Regional Head)`}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* TODO: Add input for promotion amount to check limit for regional_head */}
            <p className="text-muted-foreground">Campaign & Coupon Management Placeholder</p>
            <Button
              onClick={() => {
                // Mocking a promotion value for limit check
                const promoValue = role === "regional_head" ? 150 : 500 // Example values
                const limitExceeded = role === "regional_head" && promoValue > 200
                handleSaveChanges("Promotions", { limitExceeded })
              }}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={!(canManageGlobalPromotions || canManageRegionalPromotions)}
            >
              Add New Campaign
            </Button>
          </CardContent>
        </Card>
      )}

      {canManageAppVersions && (
        <Card>
          <CardHeader>
            <CardTitle>App Version Control</CardTitle>
            <CardDescription>Manage app versions and update notifications. (Admin Only)</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="minUserAppVersion">Min User App Version</Label>
              <Input id="minUserAppVersion" defaultValue="1.2.0" disabled={!canManageAppVersions} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minDriverAppVersion">Min Driver App Version</Label>
              <Input id="minDriverAppVersion" defaultValue="1.1.5" disabled={!canManageAppVersions} />
            </div>
            <Button
              onClick={() => handleSaveChanges("App Versions")}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={!canManageAppVersions}
            >
              Save App Versions
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
