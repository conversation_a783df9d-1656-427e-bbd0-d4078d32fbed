"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { mockDrivers } from "@/lib/mock-data"
import type { Driver } from "@/types"
import { ArrowLeft, FileText, UploadCloud, CheckCircle, XCircle, AlertTriangle, ShieldAlert } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

type DocumentStatus = "missing" | "uploaded" | "verified" | "rejected" | "expired"
interface DriverDocument {
  type: string
  fileName?: string
  status: DocumentStatus
  uploadDate?: string
  verificationDate?: string
  expiryDate?: string
  rejectionReason?: string
}

const initialDocuments: DriverDocument[] = [
  { type: "Driver's License", status: "missing" },
  { type: "Vehicle Registration (Blue Book)", status: "missing" },
  { type: "Citizenship/ID", status: "missing" },
  { type: "Vehicle Insurance", status: "missing" },
]

export default function DriverDocumentsPage() {
  const params = useParams()
  const router = useRouter()
  const { user: authUser } = useAuth()
  const { toast } = useToast()
  const driverId = params.id as string

  const [driver, setDriver] = useState<Driver | null>(null)
  const [documents, setDocuments] = useState<DriverDocument[]>(initialDocuments)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const driverData = mockDrivers.find((d) => d.id === driverId)
    if (driverData) {
      if (authUser?.role === "regional_head" && driverData.region !== authUser.region) {
        toast({
          variant: "destructive",
          title: "Access Denied",
          description: "You can only manage documents for drivers in your own region.",
        })
        router.push(`/dashboard/drivers/${driverId}`)
        return
      }
      setDriver(driverData)
      // Mock fetching existing documents - for demo, some are pre-filled
      setDocuments([
        {
          type: "Driver's License",
          fileName: "license.pdf",
          status: "verified",
          uploadDate: "2023-01-10",
          verificationDate: "2023-01-12",
          expiryDate: "2025-01-09",
        },
        {
          type: "Vehicle Registration (Blue Book)",
          fileName: "bluebook.jpg",
          status: "uploaded",
          uploadDate: "2023-01-15",
        },
        { type: "Citizenship/ID", status: "missing" },
        {
          type: "Vehicle Insurance",
          fileName: "insurance.pdf",
          status: "expired",
          uploadDate: "2022-05-01",
          verificationDate: "2022-05-03",
          expiryDate: "2023-04-30",
        },
      ])
    } else {
      toast({ variant: "destructive", title: "Error", description: "Driver not found." })
      router.push("/dashboard/drivers")
    }
    setIsLoading(false)
  }, [driverId, router, toast, authUser])

  const handleDocumentAction = (docType: string, action: "verify" | "reject" | "request_reupload") => {
    // Mock action
    setDocuments((docs) =>
      docs.map((doc) => {
        if (doc.type === docType) {
          if (action === "verify")
            return { ...doc, status: "verified", verificationDate: new Date().toISOString().split("T")[0] }
          if (action === "reject") return { ...doc, status: "rejected", rejectionReason: "Document unclear (Mock)" }
          // Request re-upload might just notify driver and set status to 'missing' or 'pending_reupload'
        }
        return doc
      }),
    )
    toast({
      title: "Document Action (Mock)",
      description: `Action '${action}' for ${docType} for driver ${driver?.name} processed.`,
    })
  }

  const getStatusBadge = (status: DocumentStatus) => {
    switch (status) {
      case "verified":
        return (
          <Badge className="bg-status-green text-status-green-foreground">
            <CheckCircle className="mr-1 h-3 w-3" />
            Verified
          </Badge>
        )
      case "uploaded":
        return (
          <Badge className="bg-status-blue text-status-blue-foreground">
            <UploadCloud className="mr-1 h-3 w-3" />
            Uploaded
          </Badge>
        )
      case "rejected":
        return (
          <Badge variant="destructive">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        )
      case "expired":
        return (
          <Badge variant="destructive" className="bg-status-orange text-status-orange-foreground">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Expired
          </Badge>
        )
      case "missing":
      default:
        return (
          <Badge variant="outline" className="border-status-gray text-status-gray">
            Missing
          </Badge>
        )
    }
  }

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading driver documents...</div>
  }

  if (!driver) {
    return null // Handled by useEffect
  }

  const canManageDocuments =
    authUser?.role === "admin" || (authUser?.role === "regional_head" && driver.region === authUser.region)

  if (!canManageDocuments) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-md">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>You do not have permission to manage documents for this driver.</AlertDescription>
        </Alert>
        <Button asChild variant="link" className="mt-4">
          <Link href={`/dashboard/drivers/${driverId}`}>Back to Driver Details</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/drivers">Drivers</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/dashboard/drivers/${driverId}`}>{driver.name || "Driver"}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Documents</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold flex items-center">
          <FileText className="mr-3 h-8 w-8 text-primary" />
          Driver Documents: {driver.name}
        </h1>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Driver
        </Button>
      </div>
      <CardDescription>Manage and verify uploaded documents for this driver.</CardDescription>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {documents.map((doc) => (
          <Card key={doc.type}>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                {doc.type}
                {getStatusBadge(doc.status)}
              </CardTitle>
              {doc.fileName && <CardDescription>File: {doc.fileName}</CardDescription>}
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              {doc.uploadDate && <p>Uploaded: {new Date(doc.uploadDate).toLocaleDateString()}</p>}
              {doc.verificationDate && <p>Verified: {new Date(doc.verificationDate).toLocaleDateString()}</p>}
              {doc.expiryDate && (
                <p className={new Date(doc.expiryDate) < new Date() ? "text-red-500 font-semibold" : ""}>
                  Expires: {new Date(doc.expiryDate).toLocaleDateString()}
                </p>
              )}
              {doc.status === "rejected" && doc.rejectionReason && (
                <p className="text-red-500">Reason: {doc.rejectionReason}</p>
              )}
            </CardContent>
            <CardFooter className="flex gap-2">
              {doc.status === "missing" && (
                <Button size="sm" variant="outline" className="bg-background text-foreground">
                  <UploadCloud className="mr-2 h-4 w-4" />
                  Upload (Mock)
                </Button>
              )}
              {doc.status === "uploaded" && (
                <>
                  <Button
                    size="sm"
                    onClick={() => handleDocumentAction(doc.type, "verify")}
                    className="bg-status-green text-status-green-foreground hover:bg-status-green/90"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Verify
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => handleDocumentAction(doc.type, "reject")}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                </>
              )}
              {(doc.status === "rejected" || doc.status === "expired") && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDocumentAction(doc.type, "request_reupload")}
                  className="bg-background text-foreground"
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Request Re-upload
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
