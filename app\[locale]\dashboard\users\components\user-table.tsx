"use client"

import Link from "next/link"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, Edit3, Trash2, UserX, UserCheck } from "lucide-react"
import type { User, UserRole } from "@/types"
import { useToast } from "@/components/ui/use-toast"
import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface UserTableProps {
  data: User[]
  currentUserRole: UserRole
  // onDeleteUser: (userId: string) => void; // Future: if local state management of deletion is needed
  // onUpdateUserStatus: (userId: string, status: UserStatus) => void; // Future
}

export function UserTable({ data, currentUserRole }: UserTableProps) {
  const { toast } = useToast()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<User | null>(null)

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user)
    setIsDeleteDialogOpen(true)
  }

  const confirmDeleteUser = () => {
    if (userToDelete) {
      // Here you would typically call an API to delete the user
      // For now, we'll just show a toast and log it.
      console.log("Deleting user:", userToDelete.id)
      toast({
        title: "User Deleted (Simulated)",
        description: `${userToDelete.name} has been deleted.`,
      })
      // If managing state locally and passing down a delete handler:
      // onDeleteUser(userToDelete.id);
    }
    setIsDeleteDialogOpen(false)
    setUserToDelete(null)
  }

  const canEditOrDelete = (user: User): boolean => {
    if (currentUserRole === "admin") return true
    if (currentUserRole === "regional_head" && user.region === user.region) {
      // Assuming regional head has a region property
      return true // Regional head can manage users in their own region
    }
    return false
  }

  const getStatusVariant = (status: User["status"]) => {
    switch (status) {
      case "active":
        return "success"
      case "inactive":
        return "secondary"
      case "pending_approval":
        return "warning"
      case "suspended":
        return "destructive"
      default:
        return "default"
    }
  }

  const formatRole = (role?: UserRole) => {
    // Allow role to be potentially undefined for safety
    if (!role) {
      return "N/A" // Handle undefined role gracefully
    }
    return role
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  if (!data || data.length === 0) {
    return <p>No users found.</p>
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Region</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{formatRole(user.role)}</TableCell>
                <TableCell>{user.region || "N/A"}</TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(user.status) as any}>
                    {" "}
                    {/* Cast to any if custom variants are not in default BadgeProps */}
                    {user.status.replace("_", " ")}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/users/${user.id}`} className="flex items-center w-full">
                          <Eye className="mr-2 h-4 w-4" /> View Details
                        </Link>
                      </DropdownMenuItem>
                      {canEditOrDelete(user) && (
                        <>
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/users/${user.id}/edit`} className="flex items-center w-full">
                              <Edit3 className="mr-2 h-4 w-4" /> Edit User
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {/* Example: Change status actions */}
                          {user.status === "pending_approval" && (
                            <DropdownMenuItem
                              onClick={() => console.log("Approve user", user.id)}
                              className="flex items-center w-full cursor-pointer"
                            >
                              <UserCheck className="mr-2 h-4 w-4 text-green-500" /> Approve User
                            </DropdownMenuItem>
                          )}
                          {(user.status === "active" || user.status === "pending_approval") && (
                            <DropdownMenuItem
                              onClick={() => console.log("Suspend user", user.id)}
                              className="flex items-center w-full cursor-pointer"
                            >
                              <UserX className="mr-2 h-4 w-4 text-orange-500" /> Suspend User
                            </DropdownMenuItem>
                          )}
                          {user.status === "suspended" && (
                            <DropdownMenuItem
                              onClick={() => console.log("Reactivate user", user.id)}
                              className="flex items-center w-full cursor-pointer"
                            >
                              <UserCheck className="mr-2 h-4 w-4 text-green-500" /> Reactivate User
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteUser(user)}
                            className="flex items-center w-full text-red-600 hover:!text-red-600 cursor-pointer"
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Delete User
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user
              <span className="font-semibold"> {userToDelete?.name}</span> and remove their data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setUserToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteUser} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
