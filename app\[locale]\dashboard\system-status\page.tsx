"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import Link from "next/link"
import { CheckCircle, AlertTriangle, XCircle, Server } from "lucide-react"

const systemComponents = [
  { name: "Main API", status: "Operational", icon: CheckCircle, color: "text-status-green" },
  { name: "Database Cluster", status: "Operational", icon: CheckCircle, color: "text-status-green" },
  { name: "Real-time Service (Sockets)", status: "Operational", icon: CheckCircle, color: "text-status-green" },
  { name: "Payment Gateway (eSewa)", status: "Degraded Performance", icon: AlertTriangle, color: "text-status-yellow" },
  { name: "Payment Gateway (Khalti)", status: "Operational", icon: CheckCircle, color: "text-status-green" },
  { name: "Push Notification Service", status: "Operational", icon: CheckCircle, color: "text-status-green" },
  { name: "Background Job Processor", status: "Minor Outage", icon: XCircle, color: "text-status-red" },
  { name: "Logging Service", status: "Operational", icon: CheckCircle, color: "text-status-green" },
]

export default function SystemStatusPage() {
  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>System Status</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-semibold">System Status</h1>
      <CardDescription>Detailed overview of all system components and their current status.</CardDescription>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {systemComponents.map((component) => {
          const Icon = component.icon
          return (
            <Card key={component.name}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{component.name}</CardTitle>
                <Icon className={`h-5 w-5 ${component.color}`} />
              </CardHeader>
              <CardContent>
                <Badge
                  variant={
                    component.status === "Operational"
                      ? "default"
                      : component.status.includes("Degraded")
                        ? "outline"
                        : "destructive"
                  }
                  className={
                    component.status === "Operational"
                      ? "bg-status-green text-status-green-foreground"
                      : component.status.includes("Degraded")
                        ? "border-status-yellow text-status-yellow"
                        : "bg-status-red text-status-red-foreground"
                  }
                >
                  {component.status}
                </Badge>
                <p className="text-xs text-muted-foreground mt-2">Last checked: Just now (Placeholder)</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Overall System Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] flex items-center justify-center bg-muted/30 rounded-lg border border-dashed">
            <p className="text-muted-foreground text-center">
              System Performance Chart Placeholder
              <br />
              <span className="text-xs">(e.g., Response Times, Error Rates)</span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
