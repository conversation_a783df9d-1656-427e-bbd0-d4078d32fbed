"use client"

import { useState, useMemo } from "react"
import { UserTable } from "./components/user-table"
import { mockUsers as initialMockUsers } from "@/lib/mock-data"
import { Button } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { AddUserForm } from "./components/add-user-form"
import type { User } from "@/types"
import { useAuth } from "@/context/auth-context"

export default function UserManagementPage() {
  const { toast } = useToast()
  const { user } = useAuth()
  const role = user?.role || "regional_operator"
  const region = user?.region

  const [usersData, setUsersData] = useState<User[]>(initialMockUsers)
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false)

  const filteredUsers = useMemo(() => {
    if (role === "admin") return usersData
    if (role === "regional_head" && region) return usersData.filter((u) => u.region === region)
    // Regional operator might see users from their region for support, or none if strictly limited
    if (role === "regional_operator" && region) return usersData.filter((u) => u.region === region)
    return [] // Default to empty if no specific view
  }, [usersData, role, region])

  const handleUserAdded = (newUser: User) => {
    // If admin or regional head adds, assign their region or default if admin
    const userWithRegion = {
      ...newUser,
      region: role === "admin" ? newUser.region || "DefaultRegion" : region || newUser.region || "DefaultRegion",
    }
    setUsersData((prevUsers) => [userWithRegion, ...prevUsers])
    toast({
      title: "User Added",
      description: `${newUser.name} has been successfully added.`,
    })
  }

  // User registration approval/denial is admin/regional_head only
  const canAddUser = role === "admin" || role === "regional_head"

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">User Management {role !== "admin" && region ? `(${region})` : ""}</h1>
        {canAddUser && (
          <Button
            onClick={() => setIsAddUserModalOpen(true)}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add User
          </Button>
        )}
      </div>
      <UserTable data={filteredUsers} currentUserRole={role} />
      {canAddUser && (
        <AddUserForm open={isAddUserModalOpen} onOpenChange={setIsAddUserModalOpen} onUserAdded={handleUserAdded} />
      )}
    </div>
  )
}
