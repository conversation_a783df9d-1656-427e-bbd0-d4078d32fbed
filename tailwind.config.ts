import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))", // Sarathi Yellow for focus ring
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#182B48", // SubhYatra Dark Blue
          foreground: "#FFFFFF", // White text on dark blue
        },
        secondary: {
          DEFAULT: "#F3F4F6", // Lighter Grey for secondary elements
          foreground: "#182B48", // Dark Blue text on light grey
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))", // Standard destructive red
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "#FFD304", // SubhYatra Yellow
          foreground: "#182B48", // Dark Blue text on yellow
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Specific status colors for consistency
        status: {
          green: "#10B981", // Emerald 500
          "green-foreground": "#FFFFFF",
          yellow: "#F59E0B", // Amber 500
          "yellow-foreground": "#1F2937", // Cool Gray 800 for better contrast on yellow
          red: "#EF4444", // Red 500
          "red-foreground": "#FFFFFF",
          blue: "#3B82F6", // Blue 500
          "blue-foreground": "#FFFFFF",
          orange: "#F97316", // Orange 500
          "orange-foreground": "#FFFFFF",
          gray: "#6B7280", // Cool Gray 500
          "gray-foreground": "#FFFFFF",
        },
      },
      fontFamily: {
        sans: ["var(--font-inter)", "var(--font-nepali)", "system-ui", "sans-serif"],
        nepali: ["var(--font-nepali)", "system-ui", "sans-serif"],
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config
