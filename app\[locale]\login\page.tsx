"use client"

import type React from "react"

import { useState } from "react"
import { useTranslations } from "next-intl"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/context/auth-context"
import { LanguageToggle } from "@/components/language-toggle"

export default function LoginPage() {
  const [email, setEmail] = useState("<EMAIL>") // Default for demo
  const [password, setPassword] = useState("password_admin") // Default for demo
  const [isLoading, setIsLoading] = useState(false)
  const { login } = useAuth()
  const t = useTranslations()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // In a real application, this form submission would occur over HTTPS.
    // The backend would receive the email and password,
    // hash the password using a strong algorithm (e.g., bcrypt),
    // and compare it against the stored hash for the user.
    console.log("Attempting login with:", { email, password })
    await login(email, password)
    // login function now handles redirection and toast messages
    setIsLoading(false)
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/40 relative">
      {/* Language Toggle in top right */}
      <div className="absolute top-4 right-4">
        <LanguageToggle />
      </div>

      <Card className="w-full max-w-sm">
        <CardHeader className="text-center">
          <Image src="/subhyatra-logo.svg" alt="SubhYatra Logo" width={80} height={80} className="mx-auto mb-4" />
          <CardTitle className="text-2xl">{t('login.title')}</CardTitle>
          <CardDescription>{t('login.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">{t('login.email')}</Label>
              <Input
                id="email"
                type="email"
                placeholder={t('login.emailPlaceholder')}
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{t('login.password')}</Label>
              <Input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
              disabled={isLoading}
            >
              {isLoading ? t('login.loggingIn') : t('login.loginButton')}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
