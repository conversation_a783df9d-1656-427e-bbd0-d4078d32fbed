import { useLocale, useTranslations } from "next-intl"

export default function HomePage() {
  const locale = useLocale()
  const tHomepage = useTranslations('homepage')

  return (
    <div className="min-h-screen p-8">
      <h1 className="text-4xl font-bold mb-4">
        {tHomepage('title')}
      </h1>
      <p className="text-xl mb-4">
        Current locale: {locale}
      </p>
      <p className="text-lg">
        {tHomepage('tagline')}
      </p>
      <div className="mt-8">
        <a
          href={`/${locale}/login`}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Login
        </a>
      </div>
    </div>
  )
}
