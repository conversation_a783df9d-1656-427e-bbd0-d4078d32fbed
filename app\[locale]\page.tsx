"use client"

import type React from "react"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useLocale, useTranslations } from "next-intl"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/context/auth-context"
import { LanguageToggle } from "@/components/language-toggle"
import { ArrowRight, Shield, Users, MapPin, Clock } from "lucide-react"

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const locale = useLocale()
  const t = useTranslations()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(`/${locale}/dashboard`)
    }
  }, [isAuthenticated, isLoading, router, locale])

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  // Don't render homepage if user is authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image
              src="/subhyatra-logo.svg"
              alt="SubhYatra Logo"
              width={40}
              height={40}
              className="h-10 w-10"
            />
            <h1 className="text-2xl font-bold text-primary">SubhYatra</h1>
          </div>
          <div className="flex items-center gap-3">
            <LanguageToggle />
            <Link href={`/${locale}/login`}>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                {t('common.login')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <div className="mb-8">
            <Image
              src="/subhyatra-logo.svg"
              alt="SubhYatra Logo"
              width={120}
              height={120}
              className="mx-auto mb-6"
            />
          </div>

          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            {t('homepage.title')}
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
            {t('homepage.tagline')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href={`/${locale}/login`}>
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg">
                {t('homepage.accessManagementCenter')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">{t('homepage.features.safeSecure.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                {t('homepage.features.safeSecure.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Users className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">{t('homepage.features.trustedCommunity.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                {t('homepage.features.trustedCommunity.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <MapPin className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">{t('homepage.features.wideCoverage.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                {t('homepage.features.wideCoverage.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <Clock className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="text-lg">{t('homepage.features.twentyFourSeven.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                {t('homepage.features.twentyFourSeven.description')}
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Management Center Access */}
        <div className="max-w-2xl mx-auto text-center">
          <Card className="bg-muted/50 border-primary/20">
            <CardHeader>
              <CardTitle className="text-2xl text-primary">{t('homepage.managementCenter.title')}</CardTitle>
              <CardDescription className="text-lg">
                {t('homepage.managementCenter.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Link href={`/${locale}/login`}>
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3">
                  {t('homepage.loginToDashboard')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/30 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <Image
                src="/subhyatra-logo.svg"
                alt="SubhYatra Logo"
                width={32}
                height={32}
                className="h-8 w-8"
              />
              <span className="text-lg font-semibold text-primary">SubhYatra</span>
            </div>
            <p className="text-muted-foreground text-center md:text-right">
              {t('common.copyright')}
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
