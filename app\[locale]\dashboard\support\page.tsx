"use client"

import React from "react"
import Link from "next/link"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context" // Added

const mockTickets = [
  {
    id: "TKT001",
    subject: "Payment Failed",
    user: "Aarav Sharma",
    status: "Open",
    lastUpdate: "2023-06-15",
    region: "Kathmandu",
  },
  {
    id: "TKT002",
    subject: "Driver Rude Behavior",
    user: "<PERSON>a Thapa",
    status: "In Progress",
    lastUpdate: "2023-06-14",
    region: "Pokhara",
  },
  {
    id: "TKT003",
    subject: "App Crashing",
    user: "<PERSON><PERSON>v",
    status: "Resolved",
    lastUpdate: "2023-06-12",
    region: "Kathmandu",
  },
  {
    id: "TKT004",
    subject: "Lost Item",
    user: "Deepika Karki",
    status: "Open",
    lastUpdate: "2023-06-16",
    region: "Biratnagar",
  },
]

export default function SupportTicketsPage() {
  const { toast } = useToast()
  const { user } = useAuth() // Added
  const role = user?.role || "regional_operator" // Added
  const region = user?.region // Added

  const filteredTickets = React.useMemo(() => {
    // Added
    if (role === "admin") return mockTickets
    if ((role === "regional_head" || role === "regional_operator") && region) {
      return mockTickets.filter((ticket) => ticket.region === region)
    }
    return []
  }, [role, region])

  // Permissions
  const canAssign = role === "admin" || role === "regional_head"
  const canResolve = role === "admin" || role === "regional_head"
  const canEscalate = role === "regional_operator" || role === "regional_head" // Regional Head can escalate to Admin

  const handleTicketAction = (action: string, ticketId: string) => {
    toast({
      title: `Ticket Action: ${action}`,
      description: `Action for ticket ${ticketId} is a placeholder. (Role: ${role})`,
    })
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">
        Customer Support Tickets {role !== "admin" && region ? `(${region})` : ""}
      </h1>
      <Card>
        <CardHeader>
          <CardTitle>Support Tickets</CardTitle>
          <CardDescription>Manage and respond to customer support requests.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ticket ID</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Update</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell>{ticket.id}</TableCell>
                  <TableCell>{ticket.subject}</TableCell>
                  <TableCell>{ticket.user}</TableCell>
                  <TableCell>
                    <Badge
                      className={
                        ticket.status === "Open"
                          ? "bg-red-500 text-white"
                          : ticket.status === "In Progress"
                            ? "bg-yellow-500 text-black"
                            : "bg-green-500 text-white"
                      }
                    >
                      {ticket.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{ticket.lastUpdate}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link href={`/dashboard/support/${ticket.id}`} className="flex items-center w-full">
                            View/Reply
                          </Link>
                        </DropdownMenuItem>
                        {canAssign && (
                          <DropdownMenuItem onClick={() => handleTicketAction("Assign", ticket.id)}>
                            Assign
                          </DropdownMenuItem>
                        )}
                        {canResolve && ticket.status !== "Resolved" && (
                          <DropdownMenuItem onClick={() => handleTicketAction("Mark as Resolved", ticket.id)}>
                            Mark as Resolved
                          </DropdownMenuItem>
                        )}
                        {canEscalate && ticket.status !== "Resolved" && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleTicketAction(
                                role === "regional_operator" ? "Escalate to Regional Head" : "Escalate to Admin",
                                ticket.id,
                              )
                            }
                          >
                            {role === "regional_operator" ? "Escalate to Regional Head" : "Escalate to Admin"}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              {filteredTickets.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No tickets found for your scope.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
