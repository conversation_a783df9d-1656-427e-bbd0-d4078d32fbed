"use client"

import * as React from "react"
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, AlertCircle, Ban, Edit2, Siren, BarChart2 } from "lucide-react"
import type { Ride, UserRole } from "@/types" // Import UserRole
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import Link from "next/link"

// Props updated to include currentUserRole and currentUserRegion
export function RideTable({
  data,
  currentUserRole,
  currentUserRegion,
}: {
  data: Ride[]
  currentUserRole: UserRole
  currentUserRegion?: string
}) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const { toast } = useToast()

  // Permissions based on role
  const canViewDetails = true // All roles can view details
  const canCancelRide = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canResolveDispute = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canAdjustFare = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canHandleEmergency = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canViewAnalytics = currentUserRole === "admin" || currentUserRole === "regional_head"
  const canBasicRefund = currentUserRole === "regional_operator" // Specific for operator

  const handleAction = (action: string, ride: Ride, details?: any) => {
    let message = `Action '${action}' on ride ID '${ride.id}'`
    if (details?.amount) message += ` for amount ${details.amount}`
    message += ` would be sent to the API. (Mocked for ${currentUserRole})`

    // Simulate limits for regional head
    if (currentUserRole === "regional_head") {
      if ((action === "Resolve Dispute" || action === "Adjust Fare") && details?.amount > 100) {
        toast({
          title: "Action Limit Exceeded",
          description: `Regional Heads can only ${action.toLowerCase()} up to $100. This action for $${details.amount} would require Admin approval.`,
          variant: "destructive",
        })
        return
      }
    }
    // Simulate limits for regional operator
    if (currentUserRole === "regional_operator" && action === "Basic Refund" && details?.amount > 25) {
      toast({
        title: "Action Limit Exceeded",
        description: `Regional Operators can only process refunds up to $25. This action for $${details.amount} would require Regional Head approval.`,
        variant: "destructive",
      })
      return
    }

    toast({
      title: `Ride Action: ${action}`,
      description: message,
    })
  }

  const columns: ColumnDef<Ride>[] = [
    { accessorKey: "id", header: "Ride ID" },
    { accessorKey: "userName", header: "User" },
    { accessorKey: "driverName", header: "Driver", cell: ({ row }) => row.original.driverName || "N/A" },
    { accessorKey: "pickupLocation", header: "Pickup" },
    { accessorKey: "dropoffLocation", header: "Dropoff" },
    { accessorKey: "fare", header: "Fare (रू)", cell: ({ row }) => `रू ${row.original.fare.toLocaleString()}` },
    { accessorKey: "rideType", header: "Type" },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status
        let badgeClass = ""
        if (status === "completed") badgeClass = "bg-green-500 hover:bg-green-600 text-white"
        else if (status === "cancelled_user" || status === "cancelled_driver")
          badgeClass = "bg-red-500 hover:bg-red-600 text-white"
        else if (
          status === "in_progress" ||
          status === "accepted" ||
          status === "en_route_pickup" ||
          status === "arrived_pickup"
        )
          badgeClass = "bg-blue-500 hover:bg-blue-600 text-white"
        else if (status === "requested") badgeClass = "bg-yellow-500 hover:bg-yellow-600 text-black"
        else if (status === "disputed") badgeClass = "bg-orange-500 hover:bg-orange-600 text-white"

        return <Badge className={cn("capitalize", badgeClass)}>{status.replace(/_/g, " ")}</Badge>
      },
    },
    {
      accessorKey: "requestTime",
      header: "Requested At",
      cell: ({ row }) => new Date(row.original.requestTime).toLocaleString(),
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const ride = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              {canViewDetails && (
                <DropdownMenuItem asChild className="cursor-pointer">
                  <Link href={`/dashboard/rides/${ride.id}`} className="flex items-center w-full">
                    <Eye className="mr-2 h-4 w-4" /> View Details
                  </Link>
                </DropdownMenuItem>
              )}

              {(ride.status === "requested" ||
                ride.status === "accepted" ||
                ride.status === "in_progress" ||
                ride.status === "en_route_pickup" ||
                ride.status === "arrived_pickup") &&
                canCancelRide && (
                  <DropdownMenuItem onClick={() => handleAction("Cancel Ride", ride)} className="text-red-600">
                    <Ban className="mr-2 h-4 w-4" /> Cancel Ride
                  </DropdownMenuItem>
                )}

              {ride.status === "disputed" && canResolveDispute && (
                <DropdownMenuItem
                  onClick={() => handleAction("Resolve Dispute", ride, { amount: ride.fare })} // Pass amount for limit check
                  className="text-orange-600"
                >
                  <AlertCircle className="mr-2 h-4 w-4" /> Resolve Dispute
                </DropdownMenuItem>
              )}

              {canAdjustFare && (
                <DropdownMenuItem
                  onClick={() => handleAction("Adjust Fare", ride, { amount: ride.fare })} // Pass amount for limit check
                  className="text-blue-600"
                >
                  <Edit2 className="mr-2 h-4 w-4" /> Adjust Fare
                </DropdownMenuItem>
              )}

              {canBasicRefund &&
                ride.status === "completed" && ( // Operator basic refund
                  <DropdownMenuItem
                    onClick={() => handleAction("Basic Refund", ride, { amount: 25 })} // Example amount for limit check
                    className="text-green-600"
                  >
                    <Edit2 className="mr-2 h-4 w-4" /> Basic Refund ($25 Limit)
                  </DropdownMenuItem>
                )}

              {canHandleEmergency && (ride.status === "requested" || ride.status === "in_progress") && (
                <DropdownMenuItem
                  onClick={() => handleAction("Handle Emergency", ride)}
                  className="text-red-700 bg-red-100"
                >
                  <Siren className="mr-2 h-4 w-4" /> Handle Emergency
                </DropdownMenuItem>
              )}
              {canViewAnalytics && (
                <DropdownMenuItem onClick={() => handleAction("View Analytics", ride)}>
                  <BarChart2 className="mr-2 h-4 w-4" /> Ride Analytics
                </DropdownMenuItem>
              )}
              {currentUserRole === "regional_operator" && (
                <DropdownMenuItem onClick={() => handleAction("Report Incident", ride)}>
                  <AlertCircle className="mr-2 h-4 w-4 text-yellow-600" /> Report Incident
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  })

  return (
    <div>
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter by User or Driver..."
          value={(table.getColumn("userName")?.getFilterValue() as string) ?? ""}
          onChange={(event) => {
            table.getColumn("userName")?.setFilterValue(event.target.value)
          }}
          className="max-w-sm"
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button variant="outline" size="sm" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage()}>
          Previous
        </Button>
        <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
          Next
        </Button>
      </div>
    </div>
  )
}
